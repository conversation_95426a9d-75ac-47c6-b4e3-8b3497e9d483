package modules

import (
	"encoding/json"
	"igameCommon/basic"
	"igameCommon/utils"
	"igameHttp/games"
	"igameHttp/types/belatra"
	"math/rand"
)

// 新生成的游戏项目
// 游戏ID: 400157
// 游戏名称: WinterThuder
// 作者: noir
// 生成时间: 2025-08-18 14:28:16

// 配置结构体
type c400157 struct {
	MaxPayout        int64              `yaml:"maxPayout"`        // 最大赔付
	Row              int                `yaml:"row"`              // 行数
	Column           int                `yaml:"column"`           // 列数
	Pattern          [][]basic.Position `yaml:"pattern"`          // 连线模式
	WildIcon         int16              `yaml:"wildIcon"`         // 百搭图标
	ScatterIcon      int16              `yaml:"scatterIcon"`      // 散布图标
	MoonIcon         int16              `yaml:"moonIcon"`         // 月亮图标
	MoonProb         int                `yaml:"moonProb"`         // 月亮概率
	MoonZeroProb     int                `yaml:"moonZeroProb"`     // 月亮为0的概率
	ThunderProb      int                `yaml:"thunderProb"`      // 闪电概率
	ThunderHitProb   int                `yaml:"thunderHitProb"`   // 闪电命中的概率
	PayoutTable      map[int16][]int64  `yaml:"payoutTable"`      // 赔付表
	IconWeight       map[int16]int32    `yaml:"iconWeight"`       // 图标权重
	MoonWeight       map[int16]int32    `yaml:"moonWeight"`       // 月亮权重
	ThunderHitWeight map[int16]int32    `yaml:"thunderHitWeight"` // 闪电命中权重
	MinLimit         map[int32]struct {
		X     int32
		Limit map[string]int32
	}
}

// 游戏模块结构体
var _ = Factory.reg(basic.NewGeneral[*m400157])

type m400157 struct {
	Config                            c400157
	RandByWeight                      *utils.RandomWeightPicker[int16, int32]
	RandByWeightWithoutScatter        *utils.RandomWeightPicker[int16, int32]
	RandByWeightWithoutMoon           *utils.RandomWeightPicker[int16, int32]
	RandByWeightWithoutScatterAndMoon *utils.RandomWeightPicker[int16, int32]
	RandByMoonWeight                  *utils.RandomWeightPicker[int16, int32]
	RandByThunderHitWeight            *utils.RandomWeightPicker[int16, int32]
}

func (m *m400157) Init(config []byte) {
	m.Config = utils.ParseYAML[c400157](config)
	withoutScatter := utils.FilterIconWeight(m.Config.IconWeight, func(icon int16) bool {
		return icon != m.Config.ScatterIcon
	})

	withoutMoon := utils.FilterIconWeight(m.Config.IconWeight, func(icon int16) bool {
		return icon != m.Config.MoonIcon
	})

	withoutScatterAndMoon := utils.FilterIconWeight(m.Config.IconWeight, func(icon int16) bool {
		return icon != m.Config.ScatterIcon && icon != m.Config.MoonIcon
	})
	m.RandByWeight = utils.NewRandomWeightPicker(m.Config.IconWeight)
	m.RandByWeightWithoutScatter = utils.NewRandomWeightPicker(withoutScatter)
	m.RandByWeightWithoutMoon = utils.NewRandomWeightPicker(withoutMoon)
	m.RandByWeightWithoutScatterAndMoon = utils.NewRandomWeightPicker(withoutScatterAndMoon)
	m.RandByMoonWeight = utils.NewRandomWeightPicker(m.Config.MoonWeight)
	m.RandByThunderHitWeight = utils.NewRandomWeightPicker(m.Config.ThunderHitWeight)
}

func (m m400157) ID() int32 {
	return 400157
}

func (m m400157) Line() int32 {
	return 25
}

func (m m400157) ClientMode() int32 {
	return basic.EnumClientMode.ONE
}

func (m m400157) Exception(code int32) string {
	s := &games.S400157{}
	return s.Exception(code)
}

func (m *m400157) ZeroSpin(ctl int32, rd *rand.Rand) basic.ISpin {
	for {
		spin := m.Spin(rd)
		if spin.Payout() == 0 {
			return spin
		}
	}
}

func (m *m400157) Spin(rd *rand.Rand) basic.ISpin {
	// 生成网格
	var gridView [][]int16
	if rd.Intn(100) < m.Config.MoonProb {
		gridView = m.generateGridView(rd, true)
	} else {
		gridView = m.generateGridView(rd, false)
	}
	grid := m.processWild(gridView)

	// 创建旋转结果
	spin := &games.S400157{
		Pays: 0,
	}

	page := games.P400157{
		Grid:     m.gridCol2Row(grid),
		GridView: m.gridCol2Row(gridView),
	}
	// 计算赔付
	page.Lines = m.calculatePayout(page.Grid, spin, &page)
	boxOut := m.initBonusBox(page.Grid, rd)
	page.Bonus.BoxIn = [][]int16{{0, 0, 0, 0, 0}, {0, 0, 0, 0, 0}, {0, 0, 0, 0, 0}, {0, 0, 0, 0, 0}}
	pageBoxOut := make([][]int16, len(boxOut))
	for i := range boxOut {
		pageBoxOut[i] = make([]int16, len(boxOut[i]))
		copy(pageBoxOut[i], boxOut[i])
	}
	page.Bonus.BoxOut = pageBoxOut

	if spin.IsFree || spin.IsHot {
		freeSpins := m.checkFreeSpinsCount(page.Grid)
		remain := freeSpins
		page.FreeInfo.Total = freeSpins
		page.FreeInfo.Remain = freeSpins
		page.FreeInfo.Award = freeSpins
		page.FreeInfo.Nesting = 1
		page.FreeInfo.WildDir = 0
		spin.Pages = append(spin.Pages, page)

		for i := 0; i <= freeSpins; i++ {
			gridView = m.generateGridView(rd, false)
			grid = m.processWild(gridView)
			freePage := games.P400157{
				Grid:     m.gridCol2Row(grid),
				GridView: m.gridCol2Row(gridView),
			}
			newFreeSpin := m.checkFreeSpinsCount(freePage.Grid)
			if newFreeSpin > 0 {
				freeSpins += newFreeSpin
				remain += newFreeSpin
			}
			freePage.FreeInfo = belatra.FreeInfo{
				Total:   freeSpins,
				Remain:  remain,
				Award:   0,
				Nesting: 1,
				WildDir: 0,
			}
			freePage.Lines = m.calculatePayout(freePage.Grid, spin, &freePage)
			freePage.Pays = int32(freePage.Pays) * int32(freeSpins)
			page.FreeInfo.AllWin += int(freePage.Pays)
			spin.Pages = append(spin.Pages, freePage)
			remain--
		}
	} else if spin.IsSpec {
		spin.Pages = append(spin.Pages, page)
		respin := 3
		remain := respin
		attempt := 0
		awardedJp := []any{}
		thunderSubGameInfo := []map[string]any{}
		var addBonusName string
		var addBonusId int32
		var addBonusWin int32
		var addBonusWinK int32
		for {
			attempt++
			// 深拷贝 boxOut 到 boxIn
			boxIn := make([][]int16, len(boxOut))
			for i := range boxOut {
				boxIn[i] = make([]int16, len(boxOut[i]))
				copy(boxIn[i], boxOut[i])
			}

			for row := range boxOut {
				for col := range boxOut[row] {
					if boxOut[row][col] == 0 {
						if rd.Intn(100) < m.Config.MoonZeroProb {
							continue
						} else if attempt > 30 {
							continue
						} else {
							boxOut[row][col] = m.RandByMoonWeight.One(rd)
							if boxOut[row][col] != 0 {
								remain = 3
							}
						}
					}
				}
			}

			boxWork := make([][]int16, len(boxOut))
			for i := range boxOut {
				boxWork[i] = make([]int16, len(boxOut[i]))
				copy(boxWork[i], boxOut[i])
			}

			newBoxOut := make([][]int16, len(boxOut))
			for i := range boxOut {
				newBoxOut[i] = make([]int16, len(boxOut[i]))
				copy(newBoxOut[i], boxOut[i])
			}

			thunderArr := []any{}
			fakeThunderArr := []any{}
			//出现闪电
			if rd.Intn(100) < m.Config.ThunderProb {
				if rd.Intn(100) < m.Config.ThunderHitProb {
					// 闪电命中
					pos := []int{}
					for row := range newBoxOut {
						for col := range newBoxOut[row] {
							if newBoxOut[row][col] != 0 {
								pos = append(pos, row*m.Config.Column+col)
							}
						}
					}
					// 取随机一个索引命中
					hitIndex := pos[rd.Intn(len(pos))]
					hitRow := hitIndex / int(m.Config.Column)
					hitCol := hitIndex % int(m.Config.Column)
					thunderArr = append(thunderArr, map[string]any{
						"row":  hitRow,
						"reel": hitCol,
					})
					newBoxOut[hitRow][hitCol] = m.RandByThunderHitWeight.One(rd)
					if newBoxOut[hitRow][hitCol] < 0 {
						awardedJp = append(awardedJp, newBoxOut[hitRow][hitCol])
						switch newBoxOut[hitRow][hitCol] {
						case -12:
							addBonusName = "wolfThunder_mini"
							addBonusId = 6
							addBonusWin += 500
							addBonusWinK += 500 / m.Line()
							thunderSubGameInfo = append(thunderSubGameInfo, m.generateSubGameInfo(6, 0, 0, 0))
						case -13:
							addBonusName = "wolfThunder_minor"
							addBonusId = 5
							addBonusWin += 1250
							addBonusWinK += 1250 / m.Line()
							thunderSubGameInfo = append(thunderSubGameInfo, m.generateSubGameInfo(5, 0, 0, 0))
						case -14:
							addBonusName = "wolfThunder_major"
							addBonusId = 4
							addBonusWin += 25000
							addBonusWinK += 25000 / m.Line()
							thunderSubGameInfo = append(thunderSubGameInfo, m.generateSubGameInfo(4, 0, 0, 0))
						case -15:
							addBonusName = "wolfThunder_grand"
							addBonusId = 3
							addBonusWin += 250000
							addBonusWinK += 250000 / m.Line()
							thunderSubGameInfo = append(thunderSubGameInfo, m.generateSubGameInfo(3, 0, 0, 0))
						}
					}
				} else {
					// 闪电命中
					pos := []int{}
					for row := range newBoxOut {
						for col := range newBoxOut[row] {
							if newBoxOut[row][col] == 0 {
								pos = append(pos, row*m.Config.Column+col)
							}
						}
					}
					// 取随机一个索引命中
					if len(pos) == 0 {
						continue
					}
					ri := rd.Intn(len(pos))
					hitIndex := pos[ri]
					hitRow := hitIndex / int(m.Config.Column)
					hitCol := hitIndex % int(m.Config.Column)
					fakeThunderArr = append(fakeThunderArr, map[string]any{
						"row":  hitRow,
						"reel": hitCol,
					})
					newBoxOut[hitRow][hitCol] = 0
				}
			}

			specPage := games.P400157{
				Grid:     page.Grid,
				GridView: page.GridView,
				Bonus: games.B400157{
					BoxIn:   boxIn,
					BoxOut:  newBoxOut,
					BoxWork: boxWork,
				},
				ShotMask:    page.ShotMask,
				SubGameInfo: []map[string]any{},
			}

			if remain == 0 {
				bonusWin := 0
				for _, row := range specPage.Bonus.BoxOut {
					for _, col := range row {
						bonusWin += int(col)
						spin.Pays += int32(col) * m.Line()
					}
				}
				specPage.SubGameInfo = append(specPage.SubGameInfo, map[string]any{
					"category":               "Bonus2",
					"type":                   "2",
					"startWin":               0,
					"prevWin":                bonusWin * int(m.Line()),
					"curWin":                 bonusWin * int(m.Line()),
					"paidWin":                -1,
					"attempt":                attempt,
					"av":                     []any{},
					"attemptResult":          0,
					"winLevel":               0,
					"rule":                   "",
					"add":                    map[string]any{},
					"onlyToBD":               nil,
					"sendRestore":            nil,
					"userChoice":             "",
					"willRespin":             false,
					"awardedJp":              awardedJp,
					"thunderArr":             []any{},
					"fakeThunderArr":         []any{},
					"thunderArrHitBeforeEnd": []any{},
					"isFakeThunder":          false,
					"isRealThunder":          false,
					"lastThunderBeforeEnd":   false,
					"respinRemain":           remain,
					"startRespin":            3,
					"willBeFull":             0,
					"addBonusName":           addBonusName,
					"addBonusId":             addBonusId,
					"addBonusWin":            addBonusWin,
					"addBonusWinK":           addBonusWinK,
				})
				specPage.SubGameInfo = append(specPage.SubGameInfo, thunderSubGameInfo...)
				spin.Pages = append(spin.Pages, specPage)
				break
			} else {
				specPage.SubGameInfo = append(specPage.SubGameInfo, map[string]any{
					"category":               "Bonus2",
					"type":                   "2",
					"startWin":               0,
					"prevWin":                0 * int(m.Line()),
					"curWin":                 0 * int(m.Line()),
					"paidWin":                -1,
					"attempt":                attempt,
					"av":                     []any{},
					"attemptResult":          0,
					"winLevel":               0,
					"rule":                   "",
					"add":                    map[string]any{},
					"onlyToBD":               nil,
					"sendRestore":            nil,
					"userChoice":             "",
					"willRespin":             true,
					"awardedJp":              awardedJp,
					"thunderArr":             thunderArr,
					"fakeThunderArr":         fakeThunderArr,
					"thunderArrHitBeforeEnd": []any{},
					"isFakeThunder":          map[bool]bool{true: true, false: false}[len(fakeThunderArr) > 0],
					"isRealThunder":          map[bool]bool{true: true, false: false}[len(thunderArr) > 0],
					"lastThunderBeforeEnd":   false,
					"respinRemain":           remain,
					"startRespin":            3,
					"willBeFull":             0,
					"addBonusName":           addBonusName,
					"addBonusId":             addBonusId,
					"addBonusWin":            addBonusWin,
					"addBonusWinK":           addBonusWinK,
				})
				specPage.SubGameInfo = append(specPage.SubGameInfo, thunderSubGameInfo...)
				spin.Pages = append(spin.Pages, specPage)
			}
			remain--
		}
	} else {
		spin.Pages = append(spin.Pages, page)
	}

	for _, page := range spin.Pages {
		spin.Pays += page.Pays
	}
	return spin
}

func (m *m400157) generateGridView(rd *rand.Rand, withMoon bool) [][]int16 {
	gridCol := make([][]int16, m.Config.Column)
	for col := 0; col < m.Config.Column; col++ {
		gridCol[col] = make([]int16, m.Config.Row)

		for row := 0; row < m.Config.Row; row++ {
			if withMoon {
				gridCol[col][row] = m.RandByWeightWithoutScatter.One(rd)
			} else {
				gridCol[col][row] = m.RandByWeightWithoutMoon.One(rd)
				// 同列最多只能有一个 scatter
				if gridCol[col][row] == m.Config.ScatterIcon {
					for i := 0; i < m.Config.Row; i++ {
						if i != row {
							gridCol[col][i] = m.RandByWeightWithoutScatterAndMoon.One(rd)
						}
					}
				}
			}
		}
		if m.Config.Row > 2 {
			if gridCol[col][1] == m.Config.WildIcon && gridCol[col][0] != m.Config.WildIcon {
				gridCol[col][2] = 0
			}
			if gridCol[col][2] == m.Config.WildIcon && gridCol[col][m.Config.Row-1] != m.Config.WildIcon {
				gridCol[col][1] = 0
			}
		}
	}
	return gridCol
}

func (m *m400157) processWild(grid [][]int16) [][]int16 {
	gridCol := make([][]int16, m.Config.Column)
	copy(gridCol, grid)
	for c := range gridCol {
		for r := range gridCol[c] {
			if r == len(gridCol[c])-1 && gridCol[c][r] == m.Config.WildIcon {
				gridCol[c] = append(gridCol[c], 0)
				if len(gridCol[c]) > m.Config.Row {
					gridCol[c] = gridCol[c][1:]
				}
			} else if r == 0 && gridCol[c][r] == m.Config.WildIcon {
				gridCol[c] = append([]int16{0}, gridCol[c]...)
				if len(gridCol[c]) > m.Config.Row {
					gridCol[c] = gridCol[c][:len(gridCol[c])-1]
				}
			} else {
				if r == 1 && gridCol[c][0] != m.Config.WildIcon {
					if gridCol[c][r] == m.Config.WildIcon {
						gridCol[c][r+1] = 0
					}
				} else if r == 2 && gridCol[c][len(gridCol[c])-1] != m.Config.WildIcon {
					if gridCol[c][r] == m.Config.WildIcon {
						gridCol[c][r-1] = 0
					}
				}
			}
		}
	}
	return gridCol
}

func (m *m400157) initBonusBox(grid [][]int16, rd *rand.Rand) [][]int16 {
	pos := []int{}
	for row := range grid {
		for col := range grid[row] {
			if grid[row][col] == m.Config.MoonIcon {
				pos = append(pos, row*m.Config.Column+col)
			}
		}
	}

	boxOut := [][]int16{{0, 0, 0, 0, 0}, {0, 0, 0, 0, 0}, {0, 0, 0, 0, 0}, {0, 0, 0, 0, 0}}
	for _, p := range pos {
		row := p / m.Config.Column
		col := p % m.Config.Column
		boxOut[row][col] = m.RandByMoonWeight.One(rd)
	}
	return boxOut
}

func (m *m400157) gridCol2Row(grid [][]int16) [][]int16 {
	gridRow := make([][]int16, m.Config.Row)
	for r := 0; r < m.Config.Row; r++ {
		gridRow[r] = make([]int16, m.Config.Column)
		for c := 0; c < m.Config.Column; c++ {
			gridRow[r][c] = grid[c][r]
		}
	}
	return gridRow
}

func (m *m400157) generateSubGameInfo(subGameType, payout, mul, remain int) map[string]any {
	switch subGameType {
	case 1:
		return map[string]any{
			"category":      "Bonus1",
			"type":          "1",
			"startWin":      0,
			"prevWin":       0,
			"curWin":        payout*mul - payout,
			"paidWin":       payout*mul - payout,
			"attempt":       0,
			"av":            []any{},
			"attemptResult": 0,
			"winLevel":      0,
			"rule":          "",
			"add":           map[string]any{},
			"onlyToBD":      nil,
			"winSum_noMult": payout,
			"winSum_Mult":   payout * mul,
		}
	case 2:
		return map[string]any{
			"category":               "Bonus2",
			"type":                   "2",
			"startWin":               0,
			"prevWin":                payout * int(m.Line()),
			"curWin":                 payout * int(m.Line()),
			"paidWin":                -1,
			"attempt":                1,
			"av":                     []any{},
			"attemptResult":          0,
			"winLevel":               0,
			"rule":                   "",
			"add":                    map[string]any{},
			"onlyToBD":               nil,
			"sendRestore":            nil,
			"userChoice":             "",
			"willRespin":             false,
			"awardedJp":              []any{},
			"thunderArr":             []any{},
			"fakeThunderArr":         []any{},
			"thunderArrHitBeforeEnd": []any{},
			"isFakeThunder":          false,
			"isRealThunder":          false,
			"lastThunderBeforeEnd":   false,
			"respinRemain":           remain,
			"startRespin":            3,
			"willBeFull":             0,
		}
	case 3:
		return map[string]any{
			"category":      "Bonus3",
			"type":          "3",
			"startWin":      0,
			"prevWin":       0,
			"curWin":        250000,
			"paidWin":       250000,
			"attempt":       0,
			"av":            []any{},
			"attemptResult": 0,
			"winLevel":      0,
			"rule":          "inBonus2",
			"add":           map[string]any{},
			"onlyToBD":      nil,
		}
	case 4:
		return map[string]any{
			"category":      "Bonus4",
			"type":          "4",
			"startWin":      0,
			"prevWin":       0,
			"curWin":        25000,
			"paidWin":       25000,
			"attempt":       0,
			"av":            []any{},
			"attemptResult": 0,
			"winLevel":      0,
			"rule":          "inBonus2",
			"add":           map[string]any{},
			"onlyToBD":      nil,
		}
	case 5:
		return map[string]any{
			"category":      "Bonus5",
			"type":          "5",
			"startWin":      0,
			"prevWin":       0,
			"curWin":        1250,
			"paidWin":       1250,
			"attempt":       0,
			"av":            []any{},
			"attemptResult": 0,
			"winLevel":      0,
			"rule":          "inBonus2",
			"add":           map[string]any{},
			"onlyToBD":      nil,
		}
	case 6:
		return map[string]any{
			"category":      "Bonus6",
			"type":          "6",
			"startWin":      0,
			"prevWin":       0,
			"curWin":        500,
			"paidWin":       500,
			"attempt":       0,
			"av":            []any{},
			"attemptResult": 0,
			"winLevel":      0,
			"rule":          "inBonus2",
			"add":           map[string]any{},
			"onlyToBD":      nil,
		}
	}
	return nil
}

func (m *m400157) calculatePayout(gridRows [][]int16, spin *games.S400157, page *games.P400157) []belatra.LinesInfo {
	scatterIcon := 0
	moonIcon := 0
	linesInfo := []belatra.LinesInfo{}

	for _, row := range gridRows {
		for _, col := range row {
			if col == m.Config.ScatterIcon {
				scatterIcon += 1
			}
			if col == m.Config.MoonIcon {
				moonIcon += 1
			}
		}
	}
	for lineID, pattern := range m.Config.Pattern {
		symbols := make([]int16, 5)
		for i, pos := range pattern {
			row := pos.Row()
			col := pos.Column()
			symbols[i] = gridRows[row][col]
		}

		if ok, mul, payout, online := m.checkLine(symbols); ok {
			linesInfo = append(linesInfo, belatra.LinesInfo{
				ID: lineID,
				Iwin: belatra.Iwin{
					Cost:   payout,
					K:      mul,
					Win:    payout * int64(mul),
					OnLine: online,
				},
			})
			page.Pays += int32(payout * int64(mul))
		}
	}
	if scatterIcon >= 3 && scatterIcon < 5 {
		spin.IsFree = true
	} else if scatterIcon == 5 {
		spin.IsHot = true
	}

	if moonIcon >= 6 {
		spin.IsSpec = true
	}

	return linesInfo
}

func (m m400157) checkLine(symbols []int16) (bool, int, int64, []int16) {
	if len(symbols) < 3 {
		return false, 1, 0, nil
	}
	firstIcon := symbols[0]
	count := 0
	mul := 0

	if firstIcon == m.Config.ScatterIcon || firstIcon == m.Config.MoonIcon {
		return false, 1, 0, nil
	}

	// 如果第一个图标是Wild
	if firstIcon == m.Config.WildIcon {
		actualIcon := m.Config.WildIcon
		for _, icon := range symbols {
			if icon != m.Config.WildIcon && icon != m.Config.ScatterIcon && icon != m.Config.MoonIcon {
				actualIcon = icon
				break
			}
		}

		if actualIcon == m.Config.WildIcon {
			for _, icon := range symbols {
				if icon == m.Config.WildIcon {
					count++
				} else if icon == m.Config.ScatterIcon || icon == m.Config.MoonIcon {
					break
				} else {
					break
				}
			}
		} else {
			for _, icon := range symbols {
				if icon == actualIcon || icon == m.Config.WildIcon {
					count++
				} else if icon == m.Config.ScatterIcon || icon == m.Config.MoonIcon {
					break
				} else {
					break
				}
			}
			firstIcon = actualIcon
		}
	} else {
		for _, icon := range symbols {
			if icon == firstIcon || icon == m.Config.WildIcon {
				count++
			} else {
				break
			}
		}
	}

	if count >= 3 {
		payout := m.Config.PayoutTable[firstIcon][count]
		online := []int16{-1, -1, -1, -1, -1}

		copy(online, symbols[:count])
		for i := 0; i < len(symbols); i++ {
			if online[i] == -1 {
				online[i] = 127
			}
		}

		if mul == 0 {
			mul = 1
		}
		return true, mul, payout, online
	}
	return false, 1, 0, nil
}

// checkFreeSpinCount 计算免费旋转次数
func (m *m400157) checkFreeSpinsCount(grid [][]int16) int {
	freeSpin := 0
	scatterIcon := m.Config.ScatterIcon
	rows := len(grid)

	if rows == 0 {
		return 0
	}

	cols := len(grid[0])
	configRow := m.Config.Row

	foundCols := uint32(0)
	maxCols := uint32(1<<cols) - 1

	for row := 0; row < rows && foundCols != maxCols; row++ {
		gridRow := grid[row] // 缓存行引用
		for col := 0; col < cols; col++ {
			colMask := uint32(1 << col)
			if foundCols&colMask != 0 {
				continue
			}

			if gridRow[col] == scatterIcon {
				freeSpin += configRow - row
				foundCols |= colMask // 标记该列已找到
			}
		}
	}

	return freeSpin
}

func (m *m400157) Salting(spin basic.ISpin, salt *rand.Rand) basic.ISpin {
	s := spin.(*games.S400157)
	s.GameId = m.ID()
	s.Line = m.Line()
	s.Row = m.Config.Row
	s.Column = m.Config.Column

	// 最大赔付限制
	if s.Pays > int32(m.Config.MaxPayout) {
		s.Pays = int32(m.Config.MaxPayout)
	}

	return s
}

func (m m400157) Rule(ctx map[string]any) string {
	ruleData := map[string]any{
		"analInfo": map[string]any{
			"VIP_maxWinFreq_big":   2183612,
			"VIP_maxWinFreq_small": 104508,
			"addBonusK":            []int{10000, 1000, 50, 20},
			"addBonusNames":        []string{"wolfThunder_grand", "wolfThunder_major", "wolfThunder_minor", "wolfThunder_mini"},
			"arrlimits_winLimitK":  []int{5000, 12000},
			"baseIncuts":           []int{1, 2, 3, 4, 5, 6, 7, 8},
			"baseReels": []any{[]int{8, 8, 8, 9, 7, 7, 7, 1, 5, 5, 10, 10, 4, 4, 6, 6, 6, 6, 1, 7, 7, 7, 2, 2, 5, 5, 5, 3, 3, 0, 0, 2, 5, 5, 5, 5, 1, 1, 7, 7, 4, 4, 6, 6, 6, 3, 8, 8, 8, 9, 7, 7, 7, 1, 5, 5, 4, 4, 6, 6, 6, 6, 1, 7, 7, 7, 2, 2, 5, 5, 5, 3, 3, 0, 0, 2, 5, 5, 5, 5, 1, 1, 7, 7, 4, 4, 6, 6, 6, 3}, []int{7, 7, 3, 10, 10, 8, 8, 8, 8, 1, 1, 5, 5, 10, 10, 5, 4, 4, 8, 8, 8, 9, 5, 5, 5, 3, 0, 0, 10, 10, 5, 5, 5, 9, 4, 8, 8, 8, 2, 5, 5, 5, 1, 1, 10, 10, 6, 6, 6, 4, 4, 7, 7, 3, 8, 8, 8, 8, 1, 1, 5, 5, 5, 4, 4, 8, 10, 10, 8, 8, 9, 5, 5, 5, 3, 0, 0, 5, 5, 5, 9, 4, 8, 8, 8, 2, 10, 10, 5, 5, 5, 1, 1, 6, 6, 6, 4, 4}, []int{4, 8, 8, 8, 8, 10, 10, 2, 2, 7, 7, 7, 7, 3, 3, 10, 10, 8, 8, 8, 8, 9, 7, 7, 7, 2, 8, 8, 4, 4, 6, 6, 6, 3, 5, 5, 5, 10, 10, 1, 7, 7, 7, 0, 0, 6, 6, 6, 4, 4, 7, 7, 7, 4, 8, 8, 8, 8, 2, 2, 7, 7, 7, 7, 3, 3, 10, 10, 10, 8, 8, 8, 8, 9, 7, 7, 7, 2, 8, 8, 4, 4, 6, 6, 6, 3, 0, 0, 6, 6, 6, 4, 4, 7, 7, 7}, []int{6, 6, 6, 3, 8, 8, 8, 8, 1, 7, 7, 5, 5, 5, 2, 2, 0, 0, 6, 6, 6, 9, 8, 8, 8, 3, 3, 10, 10, 10, 10, 5, 5, 5, 1, 1, 6, 6, 6, 4, 4, 8, 8, 2, 5, 5, 5, 10, 10, 10, 5, 6, 6, 6, 3, 8, 8, 8, 8, 1, 10, 10, 7, 7, 5, 5, 5, 2, 2, 0, 0, 6, 6, 6, 9, 8, 8, 8, 3, 3, 10, 10, 10, 5, 5, 5, 1, 1, 6, 6, 6, 4, 4, 10, 10, 8, 8, 2, 5, 5, 5, 5}, []int{2, 2, 7, 7, 7, 10, 10, 7, 3, 5, 5, 5, 3, 3, 9, 8, 8, 8, 8, 2, 10, 10, 5, 5, 5, 5, 1, 1, 0, 0, 7, 7, 4, 4, 10, 10, 6, 6, 6, 6, 3, 8, 8, 8, 8, 1, 5, 5, 10, 10, 10, 10, 5, 2, 2, 7, 7, 7, 7, 3, 5, 5, 5, 3, 10, 10, 3, 9, 8, 8, 8, 8,
				2, 5, 5, 5, 5, 1, 1, 0, 0, 7, 7, 4, 4, 6, 6, 6, 6, 3, 10, 10, 8, 8, 8, 8, 1, 5, 5, 5}},
			"bonusValues":      []int{1, 2, 3, 4, 5},
			"bonusValuesBase":  []int{0, 1, 2, 3, 4, 5, 6, 7, 8, 10, 14, 16, 18},
			"bonusValuesSuper": []int{20, 22, 24, 25, 30, 40, 50, 60, 80, 100, -12, -13, -14, -15},
			"formula": map[string]any{
				"args": []any{"betPerLine", "nlines"},
				"body": "return(betPerLine * nlines/1)",
			},
			"formulaReverse": map[string]any{
				"args": []any{"betPerGame", "nlines"},
				"body": "return(betPerGame / nlines*1)",
			},
			"freeIncuts":       []int{1, 2, 3, 4, 5, 6, 7, 8},
			"freeReels":        []any{[]int{8, 8, 8, 9, 7, 7, 7, 1, 5, 5, 4, 4, 6, 6, 6, 6, 1, 7, 7, 7, 2, 2, 5, 5, 5, 3, 3, 0, 0, 2, 5, 5, 5, 5, 1, 1, 7, 7, 4, 4, 6, 6, 6, 3}, []int{7, 7, 3, 8, 8, 8, 8, 1, 1, 5, 5, 5, 4, 4, 8, 8, 8, 9, 5, 5, 5, 3, 0, 0, 5, 5, 5, 9, 4, 8, 8, 8, 2, 5, 5, 5, 1, 1, 6, 6, 6, 4, 4}, []int{4, 8, 8, 8, 9, 8, 2, 2, 7, 7, 7, 9, 7, 3, 3, 8, 8, 8, 8, 9, 7, 7, 7, 2, 8, 8, 9, 4, 4, 6, 6, 6, 3, 5, 5, 5, 1, 9, 7, 7, 7, 0, 0, 6, 6, 6, 9, 4, 4, 7, 7, 7}, []int{6, 6, 6, 3, 9, 8, 8, 8, 8, 1, 7, 9, 7, 5, 5, 5, 2, 2, 9, 0, 0, 6, 6, 6, 9, 8, 8, 8, 3, 3, 9, 5, 5, 5, 1, 1, 6, 6, 6, 9, 4, 4, 8, 8, 2, 5, 5, 5, 5}, []int{2, 2, 7, 9, 7, 7, 7, 3, 9, 5, 5, 5, 3, 3, 9, 8, 8, 8, 8, 2, 5, 5, 5, 5, 1, 1, 9, 0, 0, 7, 7, 4, 4, 6, 6, 6, 9, 6, 3, 8, 8, 8, 8, 1, 5, 5, 5}},
			"incutIds":         []int{16},
			"lineStyles":       []any{[]int{2, 2, 2, 2, 2}, []int{1, 1, 1, 1, 1}, []int{3, 3, 3, 3, 3}, []int{0, 0, 0, 0, 0}, []int{0, 1, 2, 1, 0}, []int{3, 2, 1, 2, 3}, []int{1, 2, 3, 2, 1}, []int{2, 1, 0, 1, 2}, []int{1, 1, 0, 1, 1}, []int{2, 2, 3, 2, 2}, []int{0, 0, 1, 0, 0}, []int{3, 3, 2, 3, 3}, []int{0, 1, 0, 1, 0}, []int{3, 2, 3, 2, 3}, []int{1, 0, 1, 0, 1}, []int{2, 3, 2, 3, 2}, []int{0, 1, 1, 1, 0}, []int{3, 2, 2, 2, 3}, []int{2, 3, 3, 3, 2}, []int{1, 0, 0, 0, 1}, []int{2, 1, 2, 1, 2}, []int{1, 2, 1, 2, 1}, []int{1, 2, 2, 2, 1}, []int{1, 1, 2, 1, 1}, []int{2, 2, 1, 2, 2}},
			"maxWinFreq_big":   9205002,
			"maxWinFreq_small": 277841,
			"minScatters":      []int{3},
			"outRates_vipmode": 96.11,
			"sasAdditionalId":  "WTH",
			"sasPaytableId":    "WTH960",
			"scatterIds":       []int{9},
			"statTablo": map[string]any{
				"bigwin":     9,
				"bonus":      7,
				"epicwin":    9,
				"rtp":        96.03,
				"show":       1,
				"volatility": 9,
			},
			"symbolNames": []string{"wolf", "bear", "deer", "puma", "eagle", "symb_a", "symb_k", "symb_q", "symb_j", "scatter", "moon"},
			"volatility":  4.5,
			"wildIds":     []int{0},
		},
		"antiDynamiteBet": nil,
		"aux":             0,
		"bbLimitsWinK":    []int{11505, 5000, 12000, 11505},
		"betAssortment":   ctx["betAssortment"],
		"betPerGame":      ctx["input"],
		"betPerLine":      ctx["betPerLine"],
		"bonus": map[string]any{
			"boxIn":   []any{[]int{0, 0, 0, 0, 0}, []int{0, 0, 0, 0, 0}, []int{0, 0, 0, 0, 0}, []int{0, 0, 0, 0, 0}},
			"boxOut":  []any{[]int{0, 0, 0, 0, 0}, []int{0, 0, 0, 0, 0}, []int{0, 0, 0, 0, 0}, []int{0, 0, 0, 0, 0}},
			"boxWork": nil,
		},
		"denomAssortment_cents":  []int{1},
		"doubleActive":           "off",
		"doubleActiveDbSettings": "off",
		"doubleAssortment":       []string{"off"},
		"dramshow":               nil,
		"gamegroup":              "base",
		"gcurrency":              "",
		"gdenom":                 1,
		"helpInfo": map[string]any{
			"doubles": []any{[]any{"off", 0, 0}},
			"fg": map[string]any{
				"limit": 40,
			},
			"paytable": []any{[]any{[]int{0, 1}, []int{5, 500}, []int{4, 200}, []int{3, 100}}, []any{[]int{1, 4}, []int{5, 300}, []int{4, 150}, []int{3, 75}}, []any{[]int{2, 4}, []int{5, 250}, []int{4, 125}, []int{3, 50}}, []any{[]int{3, 4}, []int{5, 250}, []int{4, 125}, []int{3, 50}}, []any{[]int{4, 4}, []int{5, 200}, []int{4, 100}, []int{3, 25}}, []any{[]int{5, 4}, []int{5, 75}, []int{4, 25}, []int{3, 10}}, []any{[]int{6, 4}, []int{5, 50}, []int{4, 20}, []int{3, 8}}, []any{[]int{7, 4}, []int{5, 50}, []int{4, 20}, []int{3, 8}}, []any{[]int{8, 4}, []int{5, 25}, []int{4, 10}, []int{3, 5}}, []any{[]int{9, 8}}, []any{[]int{10, 8}}, []any{[]int{11, 4}}, []any{[]int{12, 4}}, []any{[]int{13, 4}}, []any{[]int{14, 4}}, []any{[]int{15, 4}}, []any{[]int{16, 16}}},
		},
		"helpseed":                true,
		"incutId":                 2,
		"isMaxFlag":               0,
		"isMaxFlag_lines":         0,
		"linesAssortment":         []int{25},
		"linesPerCredit":          1,
		"maxBetPerGame_cents":     nil,
		"maxBetPerGame_credits":   20000000,
		"minBetPerGame_cents":     nil,
		"nlines":                  25,
		"outRatesVolatility":      nil,
		"phaseCur":                "finished",
		"phaseNext":               "toIdle",
		"placedbet":               ctx["input"],
		"present":                 "no",
		"reelstate":               0,
		"saveBoxIn":               []any{[]int{0, 0, 0, 0, 0}, []int{0, 0, 0, 0, 0}, []int{0, 0, 0, 0, 0}, []int{0, 0, 0, 0, 0}},
		"setVip_inFreeSpinAlways": -1,
		"shotEffect":              false,
		"shotMask":                []any{[]int{0, 0, 0, 0, 0}, []int{0, 0, 0, 0, 0}, []int{0, 0, 0, 0, 0}, []int{0, 0, 0, 0, 0}},
		"startBox":                []any{[]int{2, 2, 1, 5, 0}, []int{2, 2, 5, 2, 0}, []int{2, 0, 5, 2, 2}, []int{2, 0, 7, 8, 2}},
		"stopBox":                 []any{[]int{2, 2, 1, 5, 0}, []int{2, 2, 5, 2, 0}, []int{2, 0, 5, 2, 2}, []int{2, 0, 7, 8, 2}},
		"tmpWin":                  0,
		"versions": map[string]any{
			"server_core": "1.1",
			"server_game": "1.0",
			"server_math": "1.0",
		},
		"vipMode": map[string]any{
			"on":             1,
			"vipBetK":        2,
			"vip_noSpecSeed": true,
			"wasBuyVip":      2,
		},
		"winValidation": map[string]any{
			"isApproved":                   false,
			"isNotApproved":                false,
			"isWaitApprove":                false,
			"needcheck":                    false,
			"period":                       86400000,
			"remaintime":                   86400000,
			"winlimit_fictiveRotate_gcurr": 25000000,
		},
		"winlimits": map[string]any{
			"maxWinLimitK":         12000,
			"maxWin_gcurr":         nil,
			"needControlJackpot":   true,
			"winLimitK_gameconfig": 12000,
		},
	}

	b, _ := json.Marshal(ruleData)
	return string(b)
}

func (m m400157) InputCoef(ctl int32) int32 {
	switch ctl {
	case 1:
		return 200
	default:
		return 100
	}
}

func (m m400157) MinPayout(ctl int32) int32 {
	mode, ok := m.Config.MinLimit[ctl]
	if !ok {
		return 0
	}
	return mode.X * m.Line()
}
